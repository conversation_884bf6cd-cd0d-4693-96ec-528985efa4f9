<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🚀 Lazy Loading Performance Test</title>
  <style>
    body {
      font-family: 'Amazon Ember', Arial, sans-serif;
      margin: 20px;
      background: #f8f9fa;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .performance-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin: 20px 0;
    }
    .metric-card {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #007bff;
    }
    .metric-label {
      font-size: 14px;
      color: #666;
      margin-top: 4px;
    }
    .test-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
    }
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    .status-success { background: #28a745; }
    .status-warning { background: #ffc107; }
    .status-error { background: #dc3545; }
    .chart-container {
      height: 300px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      margin: 16px 0;
      position: relative;
      overflow: hidden;
    }
    .log-output {
      background: #1e1e1e;
      color: #00ff00;
      padding: 16px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
      margin: 16px 0;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 8px;
      font-size: 14px;
    }
    button:hover {
      background: #0056b3;
    }
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
  </style>
  
  <!-- Performance Optimization Scripts (load first) -->
  <link rel="stylesheet" href="performance-optimizations/lazy-loading-styles.css">
  <script src="performance-optimizations/event-cleanup-manager.js"></script>
  <script src="performance-optimizations/data-cache-manager.js"></script>
  <script src="performance-optimizations/dom-optimizer.js"></script>
  <script src="performance-optimizations/viewport-lazy-loader.js"></script>
  <script src="performance-optimizations/memory-monitor.js"></script>
</head>
<body>
  <div class="test-container">
    <h1>🚀 Lazy Loading Performance Test</h1>
    <p>This test validates the lazy loading implementation for dashboard charts and measures performance improvements.</p>
    
    <!-- Performance Metrics -->
    <div class="performance-metrics">
      <div class="metric-card">
        <div class="metric-value" id="initial-load-time">--</div>
        <div class="metric-label">Initial Load Time (ms)</div>
      </div>
      <div class="metric-card">
        <div class="metric-value" id="memory-usage">--</div>
        <div class="metric-label">Memory Usage (MB)</div>
      </div>
      <div class="metric-card">
        <div class="metric-value" id="charts-loaded">0/3</div>
        <div class="metric-label">Charts Loaded</div>
      </div>
      <div class="metric-card">
        <div class="metric-value" id="lazy-loader-status">--</div>
        <div class="metric-label">Lazy Loader Status</div>
      </div>
    </div>
    
    <!-- Test Controls -->
    <div class="test-section">
      <h3>Test Controls</h3>
      <button onclick="runPerformanceTest()">🚀 Run Performance Test</button>
      <button onclick="testLazyLoading()">📊 Test Lazy Loading</button>
      <button onclick="testMemoryMonitoring()">🧠 Test Memory Monitoring</button>
      <button onclick="clearLogs()">🗑️ Clear Logs</button>
    </div>
    
    <!-- Mock Chart Containers -->
    <div class="test-section">
      <h3>Mock Chart Containers (for testing lazy loading)</h3>
      
      <div id="test-last-week-chart-container" class="chart-container">
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar" style="height: 60%;"></div>
            <div class="chart-skeleton-bar" style="height: 80%;"></div>
            <div class="chart-skeleton-bar" style="height: 45%;"></div>
            <div class="chart-skeleton-bar" style="height: 90%;"></div>
            <div class="chart-skeleton-bar" style="height: 70%;"></div>
          </div>
        </div>
      </div>
      
      <div id="test-monthly-chart-container" class="chart-container">
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar" style="height: 40%;"></div>
            <div class="chart-skeleton-bar" style="height: 55%;"></div>
            <div class="chart-skeleton-bar" style="height: 70%;"></div>
            <div class="chart-skeleton-bar" style="height: 85%;"></div>
            <div class="chart-skeleton-bar" style="height: 90%;"></div>
          </div>
        </div>
      </div>
      
      <div id="test-today-chart-container" class="chart-container">
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar" style="height: 30%;"></div>
            <div class="chart-skeleton-bar" style="height: 45%;"></div>
            <div class="chart-skeleton-bar" style="height: 60%;"></div>
            <div class="chart-skeleton-bar" style="height: 75%;"></div>
            <div class="chart-skeleton-bar" style="height: 90%;"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Log Output -->
    <div class="test-section">
      <h3>Test Logs</h3>
      <div id="log-output" class="log-output">
        Ready to run performance tests...
      </div>
    </div>
  </div>

  <script>
    // Performance testing utilities
    let testStartTime = 0;
    let chartsLoaded = 0;
    let memoryMonitor = null;
    
    function log(message) {
      const logOutput = document.getElementById('log-output');
      const timestamp = new Date().toLocaleTimeString();
      logOutput.innerHTML += `[${timestamp}] ${message}\n`;
      logOutput.scrollTop = logOutput.scrollHeight;
    }
    
    function updateMetric(id, value) {
      document.getElementById(id).textContent = value;
    }
    
    function clearLogs() {
      document.getElementById('log-output').innerHTML = 'Logs cleared...\n';
    }
    
    function runPerformanceTest() {
      log('🚀 Starting comprehensive performance test...');
      testStartTime = performance.now();
      
      // Test 1: Check if performance optimization scripts are loaded
      testOptimizationScripts();
      
      // Test 2: Test lazy loading system
      testLazyLoading();
      
      // Test 3: Test memory monitoring
      testMemoryMonitoring();
      
      // Test 4: Measure initial load performance
      measureInitialLoadPerformance();
    }
    
    function testOptimizationScripts() {
      log('📋 Testing optimization scripts availability...');
      
      const scripts = [
        { name: 'ViewportLazyLoader', obj: window.ViewportLazyLoader },
        { name: 'EventCleanupManager', obj: window.EventCleanupManager },
        { name: 'DataCacheManager', obj: window.DataCacheManager },
        { name: 'MemoryMonitor', obj: window.MemoryMonitor },
        { name: 'DashboardLazyConfigs', obj: window.DashboardLazyConfigs }
      ];
      
      scripts.forEach(script => {
        if (script.obj) {
          log(`✅ ${script.name} loaded successfully`);
        } else {
          log(`❌ ${script.name} not found`);
        }
      });
      
      updateMetric('lazy-loader-status', window.ViewportLazyLoader ? '✅ Ready' : '❌ Missing');
    }
    
    function testLazyLoading() {
      log('📊 Testing lazy loading system...');
      
      if (!window.ViewportLazyLoader) {
        log('❌ ViewportLazyLoader not available');
        return;
      }
      
      // Register test chart containers for lazy loading
      const testConfigs = {
        testLastWeek: {
          id: 'test-last-week-chart',
          type: 'chart',
          loadingText: 'Loading test chart...',
          initFunction: async (element) => {
            log('📊 Lazy loading triggered for Last Week chart');
            chartsLoaded++;
            updateMetric('charts-loaded', `${chartsLoaded}/3`);
            // Simulate chart loading
            setTimeout(() => {
              element.innerHTML = '<div style="padding: 20px; text-align: center; color: #28a745;">✅ Last Week Chart Loaded</div>';
            }, 500);
          }
        },
        testMonthly: {
          id: 'test-monthly-chart',
          type: 'chart',
          loadingText: 'Loading monthly chart...',
          initFunction: async (element) => {
            log('📊 Lazy loading triggered for Monthly chart');
            chartsLoaded++;
            updateMetric('charts-loaded', `${chartsLoaded}/3`);
            setTimeout(() => {
              element.innerHTML = '<div style="padding: 20px; text-align: center; color: #28a745;">✅ Monthly Chart Loaded</div>';
            }, 500);
          }
        },
        testToday: {
          id: 'test-today-chart',
          type: 'chart',
          loadingText: 'Loading today chart...',
          initFunction: async (element) => {
            log('📊 Lazy loading triggered for Today chart');
            chartsLoaded++;
            updateMetric('charts-loaded', `${chartsLoaded}/3`);
            setTimeout(() => {
              element.innerHTML = '<div style="padding: 20px; text-align: center; color: #28a745;">✅ Today Chart Loaded</div>';
            }, 500);
          }
        }
      };
      
      // Register charts for lazy loading
      const containers = [
        { element: document.getElementById('test-last-week-chart-container'), config: testConfigs.testLastWeek },
        { element: document.getElementById('test-monthly-chart-container'), config: testConfigs.testMonthly },
        { element: document.getElementById('test-today-chart-container'), config: testConfigs.testToday }
      ];
      
      containers.forEach(({ element, config }) => {
        if (element) {
          window.ViewportLazyLoader.registerComponent(element, config);
          log(`📋 Registered ${config.id} for lazy loading`);
        }
      });
      
      log('✅ Lazy loading test setup complete');
    }
    
    function testMemoryMonitoring() {
      log('🧠 Testing memory monitoring system...');
      
      if (!window.MemoryMonitor) {
        log('❌ MemoryMonitor not available');
        return;
      }
      
      try {
        memoryMonitor = new window.MemoryMonitor();
        memoryMonitor.startMonitoring();
        log('✅ Memory monitoring started successfully');
        
        // Update memory usage display
        setInterval(() => {
          if (performance.memory) {
            const memoryUsed = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            updateMetric('memory-usage', `${memoryUsed} MB`);
          }
        }, 1000);
        
      } catch (error) {
        log(`❌ Memory monitoring failed: ${error.message}`);
      }
    }
    
    function measureInitialLoadPerformance() {
      log('⏱️ Measuring initial load performance...');
      
      const loadTime = performance.now() - testStartTime;
      updateMetric('initial-load-time', Math.round(loadTime));
      
      if (loadTime < 100) {
        log(`✅ Excellent load time: ${Math.round(loadTime)}ms`);
      } else if (loadTime < 500) {
        log(`⚠️ Good load time: ${Math.round(loadTime)}ms`);
      } else {
        log(`❌ Slow load time: ${Math.round(loadTime)}ms`);
      }
    }
    
    // Initialize test on page load
    window.addEventListener('load', () => {
      log('🎯 Performance test page loaded');
      testOptimizationScripts();
    });
  </script>
</body>
</html>
